#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面验证码自动提取并转发到微信的程序
支持监控右下角弹窗，识别验证码并自动发送到指定微信联系人
"""

import time
import re
import threading
import tkinter as tk
from tkinter import messagebox, ttk
import pyautogui
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
import win32gui
import win32con
import win32api
import logging
from datetime import datetime
import json
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('verification_forwarder.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class VerificationCodeForwarder:
    def __init__(self):
        self.is_monitoring = False
        self.monitor_thread = None
        self.config_file = 'config.json'
        self.load_config()
        
        # OCR配置
        self.setup_tesseract()
        
        # 验证码正则表达式模式
        self.code_patterns = [
            r'\b\d{4,8}\b',  # 4-8位数字
            r'\b[A-Z0-9]{4,8}\b',  # 4-8位字母数字组合
            r'验证码[：:]\s*([A-Z0-9]{4,8})',  # 包含"验证码"文字的
            r'code[：:]\s*([A-Z0-9]{4,8})',  # 包含"code"的
        ]
        
        # 创建GUI
        self.create_gui()
        
    def load_config(self):
        """加载配置文件"""
        default_config = {
            'monitor_region': [1400, 800, 520, 220],  # 右下角区域 [x, y, width, height]
            'check_interval': 2,  # 检查间隔(秒)
            'wechat_contact': '',  # 微信联系人
            'tesseract_path': r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in self.config:
                        self.config[key] = value
            except Exception as e:
                logging.error(f"加载配置文件失败: {e}")
                self.config = default_config
        else:
            self.config = default_config
            self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def setup_tesseract(self):
        """设置Tesseract OCR"""
        tesseract_path = self.config.get('tesseract_path')
        if tesseract_path and os.path.exists(tesseract_path):
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        else:
            logging.warning("Tesseract路径未设置或不存在，请确保已安装Tesseract OCR")
    
    def create_gui(self):
        """创建图形界面"""
        self.root = tk.Tk()
        self.root.title("验证码自动转发器")
        self.root.geometry("500x400")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="5")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 微信联系人
        ttk.Label(config_frame, text="微信联系人:").grid(row=0, column=0, sticky=tk.W)
        self.contact_var = tk.StringVar(value=self.config.get('wechat_contact', ''))
        ttk.Entry(config_frame, textvariable=self.contact_var, width=30).grid(row=0, column=1, padx=5)
        
        # 检查间隔
        ttk.Label(config_frame, text="检查间隔(秒):").grid(row=1, column=0, sticky=tk.W)
        self.interval_var = tk.StringVar(value=str(self.config.get('check_interval', 2)))
        ttk.Entry(config_frame, textvariable=self.interval_var, width=10).grid(row=1, column=1, padx=5, sticky=tk.W)
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=2, pady=10)
        
        self.start_btn = ttk.Button(control_frame, text="开始监控", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="测试截图", command=self.test_screenshot).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="设置区域", command=self.set_monitor_region).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="5")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        self.status_text = tk.Text(status_frame, height=15, width=60)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """在GUI中显示日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        
        self.status_text.insert(tk.END, log_msg)
        self.status_text.see(tk.END)
        logging.info(message)
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
            
        # 保存配置
        self.config['wechat_contact'] = self.contact_var.get()
        self.config['check_interval'] = float(self.interval_var.get())
        self.save_config()
        
        if not self.config['wechat_contact']:
            messagebox.showerror("错误", "请设置微信联系人")
            return
        
        self.is_monitoring = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.log_message("开始监控桌面验证码...")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.log_message("停止监控")
    
    def monitor_loop(self):
        """监控循环"""
        last_screenshot_hash = None
        
        while self.is_monitoring:
            try:
                # 截取监控区域
                screenshot = self.capture_monitor_region()
                if screenshot is None:
                    time.sleep(self.config['check_interval'])
                    continue
                
                # 计算截图哈希，避免重复处理相同内容
                screenshot_hash = hash(screenshot.tobytes())
                if screenshot_hash == last_screenshot_hash:
                    time.sleep(self.config['check_interval'])
                    continue
                
                last_screenshot_hash = screenshot_hash
                
                # OCR识别文字
                text = self.extract_text_from_image(screenshot)
                if not text.strip():
                    time.sleep(self.config['check_interval'])
                    continue
                
                # 查找验证码
                verification_code = self.find_verification_code(text)
                if verification_code:
                    self.log_message(f"发现验证码: {verification_code}")
                    self.send_to_wechat(verification_code)
                
            except Exception as e:
                self.log_message(f"监控过程中出错: {e}")
            
            time.sleep(self.config['check_interval'])
